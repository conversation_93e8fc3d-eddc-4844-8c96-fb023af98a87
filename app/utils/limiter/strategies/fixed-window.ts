import type { Emitter } from '@kdt310722/utils/event'
import type { Awaitable } from '@kdt310722/utils/promise'
import Bottleneck from 'bottleneck'
import { type LimiterScheduleOptions, Strategy, type StrategyEvents } from './strategy'

export interface FixedWindowStrategyConfig {
    capacity: number
    window: number
}

export class FixedWindowStrategy extends Strategy<FixedWindowStrategyConfig> {
    protected readonly bottleneck: Bottleneck

    public constructor(config: FixedWindowStrategyConfig, emitter: Emitter<StrategyEvents>) {
        super(config, emitter)

        this.bottleneck = this.createBottleneck(config)
    }

    public async schedule<T>(fn: () => Awaitable<T>, { weight = 1, priority = 5 }: LimiterScheduleOptions = {}) {
        return this.bottleneck.schedule({ weight, priority }, async () => fn())
    }

    protected createBottleneck(config: FixedWindowStrategyConfig) {
        const bottleneck = new Bottleneck({
            reservoir: config.capacity,
            reservoirRefreshInterval: config.window,
            reservoirRefreshAmount: config.capacity,
            minTime: 0,
        })

        bottleneck.on('error', (error) => this.emitter.emit('error', error))

        bottleneck.on('depleted', () => {
            // Note: We cannot accurately predict when Bottleneck will refresh the reservoir
            // because it uses internal timing that we don't have access to.
            // This is a best-effort estimate that the next request might be processed
            // within the next window period.
            const now = Date.now()
            const estimatedNextAvailable = new Date(now + config.window)

            this.emitter.emit('limit', estimatedNextAvailable, {
                note: 'This is an estimate - actual timing may vary based on Bottleneck internal scheduling',
            })
        })

        return bottleneck
    }
}
