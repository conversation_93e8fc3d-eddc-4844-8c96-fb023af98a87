import { Limiter } from '../app/utils/limiter/limiter'

async function debugUntilTiming(): Promise<void> {
    console.log('🔍 Debug Until Timing in Limit Events')
    
    const limiter = new Limiter({ maxRequestsPerSecond: 2 })
    
    const events: Array<{ timestamp: number, until: Date, actualNext: number }> = []
    
    limiter.on('limit', (until, metadata) => {
        const now = Date.now()
        events.push({
            timestamp: now,
            until,
            actualNext: until.getTime()
        })
        
        console.log(`⏰ Limit event at: ${new Date(now).toISOString()}`)
        console.log(`📅 Until time: ${until.toISOString()}`)
        console.log(`⏱️  Time diff: ${until.getTime() - now}ms`)
        console.log(`---`)
    })
    
    // Mock request
    const mockRequest = (id: string) => async () => {
        const start = Date.now()
        console.log(`📤 Request ${id} started at: ${new Date(start).toISOString()}`)
        await new Promise(resolve => setTimeout(resolve, 50))
        const end = Date.now()
        console.log(`✅ Request ${id} completed at: ${new Date(end).toISOString()}`)
        return `result-${id}`
    }
    
    console.log('\n--- Triggering limit with 4 requests (limit is 2) ---')
    const startTime = Date.now()
    console.log(`🚀 Test started at: ${new Date(startTime).toISOString()}`)
    
    // Send 4 requests to trigger limit
    const promises = []
    for (let i = 1; i <= 4; i++) {
        promises.push(limiter.schedule(mockRequest(i.toString())))
    }
    
    await Promise.all(promises)
    
    const endTime = Date.now()
    console.log(`🏁 Test completed at: ${new Date(endTime).toISOString()}`)
    console.log(`⏱️  Total duration: ${endTime - startTime}ms`)
    
    // Analyze events
    console.log('\n--- Event Analysis ---')
    events.forEach((event, index) => {
        const timeDiff = event.actualNext - event.timestamp
        const isReasonable = timeDiff > 0 && timeDiff <= 1100 // Should be within next window
        
        console.log(`Event ${index + 1}:`)
        console.log(`  Timestamp: ${new Date(event.timestamp).toISOString()}`)
        console.log(`  Until: ${event.until.toISOString()}`)
        console.log(`  Time diff: ${timeDiff}ms`)
        console.log(`  Reasonable: ${isReasonable ? '✅' : '❌'}`)
        
        if (!isReasonable) {
            console.log(`  ⚠️  Expected: 0-1100ms, Got: ${timeDiff}ms`)
        }
    })
    
    // Test actual timing
    console.log('\n--- Testing Actual Window Reset ---')
    if (events.length > 0) {
        const lastEvent = events[events.length - 1]
        const waitTime = lastEvent.actualNext - Date.now() + 100 // Wait until after predicted reset
        
        if (waitTime > 0) {
            console.log(`⏳ Waiting ${waitTime}ms for window reset...`)
            await new Promise(resolve => setTimeout(resolve, waitTime))
        }
        
        console.log('🔄 Testing if window actually reset...')
        const resetTestStart = Date.now()
        
        await Promise.all([
            limiter.schedule(mockRequest('reset-1')),
            limiter.schedule(mockRequest('reset-2'))
        ])
        
        const resetTestDuration = Date.now() - resetTestStart
        console.log(`⏱️  Reset test duration: ${resetTestDuration}ms`)
        
        if (resetTestDuration < 500) {
            console.log('✅ Window appears to have reset correctly')
        } else {
            console.log('❌ Window may not have reset as expected')
        }
    }
}

// Run debug
debugUntilTiming().catch(console.error)
