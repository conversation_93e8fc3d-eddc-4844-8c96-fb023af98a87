# Test Documentation

## Limiter Tests

File test `test/limiter.test.ts` kiểm tra tính năng rate limiting của class `Limiter`.

### Cách chạy test

```bash
# Chạy full test suite (comprehensive)
npm run test:limiter

# Chạy quick demo (để xem behavior cơ bản)
npm run test:limiter:quick
```

### Các test cases được bao gồm

#### 1. Basic Functionality Tests

- **Basic Limiter Creation**: Kiểm tra tạo limiter thành công và xử lý config không hợp lệ
- **Requests Under Limit**: <PERSON><PERSON><PERSON> tra requests dưới giới hạn được xử lý ngay lập tức
- **Requests At Limit**: <PERSON><PERSON><PERSON> tra requests đúng bằng giới hạn
- **Requests Over Limit**: Kiể<PERSON> tra requests vượt giới hạn bị delay đến window tiếp theo

#### 2. Event Testing

- **Limit Event Emission**: Kiểm tra event `limit` được emit đúng cách với:
    - `until`: Date object chỉ thời điểm window tiếp theo
    - `metadata`: Object chứa thông tin bổ sung
- **Error Event Emission**: Kiểm tra event `error` được emit khi có lỗi trong request
- **Multiple Limit Events**: Kiểm tra nhiều limit events được emit đúng cách

#### 3. Advanced Features

- **Concurrent Requests with Priority**: Kiểm tra xử lý requests với priority khác nhau
- **Request Weight**: Kiểm tra requests với weight khác nhau ảnh hưởng đến rate limiting
- **Window Reset**: Kiểm tra window được reset sau 1 giây và cho phép requests mới

#### 4. Edge Cases & Stability

- **Race Conditions**: Kiểm tra xử lý nhiều requests đồng thời
- **Zero Capacity Error**: Kiểm tra xử lý config với capacity = 0
- **Negative Capacity Behavior**: Kiểm tra behavior với capacity âm (không crash)
- **High Volume Stability**: Kiểm tra stability với volume cao (50 requests)

### Behavior được kiểm tra

#### Rate Limiting Logic

- **Giới hạn tối đa x requests / x giây**: ✅ Verified
- **Request dưới limit được xử lý ngay**: ✅ Verified
- **Request vượt limit bị delay đến window tiếp theo**: ✅ Verified
- **Không cần đợi request cũ hoàn thành**: ✅ Verified

#### Event System

- **Event `limit` với `until` (Date) và `metadata` (Object)**: ✅ Verified
- **Event `error` khi có lỗi**: ✅ Verified

#### Advanced Features

- **Priority system**: ✅ Verified
- **Weight system**: ✅ Verified
- **Window reset mechanism**: ✅ Verified

### Test Architecture

Tests được viết theo pattern:

- Không sử dụng test framework external
- Chạy trực tiếp với swc-node
- Custom test runner với error handling
- Mock functions cho simulation requests
- Timing-based assertions cho rate limiting behavior

### Performance Expectations

- Requests under limit: < 500ms
- Window reset: ~1100ms (1s window + buffer)
- High volume (50 requests @ 5/sec): ~8-10 seconds
- Race conditions properly handled without data corruption

### Notes

- Test `Negative Capacity Behavior` sử dụng timeout để tránh hang
- Một số tests có timing requirements và có thể fail trên systems chậm
- Console output được sử dụng để debug (có thể trigger linter warnings)
