import { Limiter } from '../app/utils/limiter/limiter'

// Quick demo test for Limiter functionality
async function quickDemo(): Promise<void> {
    console.log('🚀 Quick Limiter Demo')
    
    // Create limiter: max 3 requests per second
    const limiter = new Limiter({ maxRequestsPerSecond: 3 })
    
    // Listen for limit events
    limiter.on('limit', (until, metadata) => {
        console.log(`⏰ Rate limit hit! Next window at: ${until.toISOString()}`)
        console.log(`📊 Metadata:`, metadata)
    })
    
    // Mock request function
    const mockRequest = (id: string) => async () => {
        console.log(`📤 Request ${id} started at ${new Date().toISOString()}`)
        await new Promise(resolve => setTimeout(resolve, 100)) // 100ms delay
        console.log(`✅ Request ${id} completed at ${new Date().toISOString()}`)
        return `result-${id}`
    }
    
    console.log('\n--- Test 1: Under limit (2 requests, limit is 3) ---')
    const startTime1 = Date.now()
    
    await Promise.all([
        limiter.schedule(mockRequest('1')),
        limiter.schedule(mockRequest('2'))
    ])
    
    console.log(`⏱️  Duration: ${Date.now() - startTime1}ms (should be fast)\n`)
    
    console.log('--- Test 2: Over limit (5 requests, limit is 3) ---')
    const startTime2 = Date.now()
    
    const promises = []
    for (let i = 3; i <= 7; i++) {
        promises.push(limiter.schedule(mockRequest(i.toString())))
    }
    
    await Promise.all(promises)
    
    console.log(`⏱️  Duration: ${Date.now() - startTime2}ms (should be ~1+ seconds due to rate limiting)\n`)
    
    console.log('--- Test 3: Window reset demonstration ---')
    console.log('Waiting for window to reset...')
    await new Promise(resolve => setTimeout(resolve, 1100)) // Wait for window reset
    
    const startTime3 = Date.now()
    
    await Promise.all([
        limiter.schedule(mockRequest('8')),
        limiter.schedule(mockRequest('9')),
        limiter.schedule(mockRequest('10'))
    ])
    
    console.log(`⏱️  Duration: ${Date.now() - startTime3}ms (should be fast again after reset)\n`)
    
    console.log('🎉 Demo completed successfully!')
}

// Run demo
quickDemo().catch(console.error)
