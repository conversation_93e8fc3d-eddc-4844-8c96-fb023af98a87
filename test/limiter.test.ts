import { Limiter } from '../app/utils/limiter/limiter'

// Test utilities
function delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms))
}

function createMockRequest(id: string, duration = 10): () => Promise<string> {
    return async () => {
        await delay(duration)

        return `request-${id}-completed`
    }
}

async function runTest(testName: string, testFn: () => Promise<void>): Promise<void> {
    console.log(`\n🧪 Running: ${testName}`)

    try {
        await testFn()
        console.log(`✅ PASSED: ${testName}`)
    } catch (error) {
        console.log(`❌ FAILED: ${testName}`)
        console.error(error)
        throw error
    }
}

// Test cases
async function testBasicLimiterCreation(): Promise<void> {
    const limiter = new Limiter({ maxRequestsPerSecond: 5 })

    if (!limiter) {
        throw new Error('Limiter should be created successfully')
    }

    // Test invalid config
    try {
        new Limiter({ maxRequestsPerSecond: 0 })
        throw new Error('Should throw error for invalid config')
    } catch (error) {
        if (!(error as Error).message.includes('Invalid limiter configuration')) {
            throw new Error('Should throw specific error for invalid config')
        }
    }
}

async function testRequestsUnderLimit(): Promise<void> {
    const limiter = new Limiter({ maxRequestsPerSecond: 3 })
    const startTime = Date.now()

    // Execute 2 requests (under limit of 3)
    const results = await Promise.all([
        limiter.schedule(createMockRequest('1')),
        limiter.schedule(createMockRequest('2')),
    ])

    const endTime = Date.now()
    const duration = endTime - startTime

    if (results.length !== 2) {
        throw new Error('Should complete 2 requests')
    }

    if (!results.every((r) => r.includes('completed'))) {
        throw new Error('All requests should complete successfully')
    }

    // Should complete quickly since under limit
    if (duration > 500) {
        throw new Error(`Requests under limit should complete quickly, took ${duration}ms`)
    }
}

async function testRequestsAtLimit(): Promise<void> {
    const limiter = new Limiter({ maxRequestsPerSecond: 2 })
    const startTime = Date.now()

    // Execute exactly 2 requests (at limit)
    const results = await Promise.all([
        limiter.schedule(createMockRequest('1')),
        limiter.schedule(createMockRequest('2')),
    ])

    const endTime = Date.now()
    const duration = endTime - startTime

    if (results.length !== 2) {
        throw new Error('Should complete 2 requests')
    }

    // Should complete within reasonable time
    if (duration > 500) {
        throw new Error(`Requests at limit should complete quickly, took ${duration}ms`)
    }
}

async function testRequestsOverLimit(): Promise<void> {
    const limiter = new Limiter({ maxRequestsPerSecond: 2 })
    const startTime = Date.now()

    // Execute 4 requests (over limit of 2)
    const promises = [
        limiter.schedule(createMockRequest('1')),
        limiter.schedule(createMockRequest('2')),
        limiter.schedule(createMockRequest('3')),
        limiter.schedule(createMockRequest('4')),
    ]

    const results = await Promise.all(promises)
    const endTime = Date.now()
    const duration = endTime - startTime

    if (results.length !== 4) {
        throw new Error('Should complete all 4 requests')
    }

    // Should take at least 1 second due to rate limiting
    if (duration < 900) {
        throw new Error(`Over-limit requests should be delayed, took only ${duration}ms`)
    }

    if (duration > 2000) {
        throw new Error(`Should not take too long, took ${duration}ms`)
    }
}

async function testLimitEventEmission(): Promise<void> {
    const limiter = new Limiter({ maxRequestsPerSecond: 2 })

    let limitEventFired = false
    let eventUntil: Date | null = null
    let eventMetadata: any = null

    limiter.on('limit', (until, metadata) => {
        limitEventFired = true
        eventUntil = until
        eventMetadata = metadata
    })

    // Trigger limit by sending 3 requests
    const promises = [
        limiter.schedule(createMockRequest('1')),
        limiter.schedule(createMockRequest('2')),
        limiter.schedule(createMockRequest('3')),
    ]

    // Wait a bit for the limit event to fire
    await delay(100)

    if (!limitEventFired) {
        throw new Error('Limit event should be fired when over capacity')
    }

    if (!(eventUntil instanceof Date)) {
        throw new TypeError('Until parameter should be a Date object')
    }

    if (eventUntil.getTime() <= Date.now()) {
        throw new Error('Until date should be in the future')
    }

    if (typeof eventMetadata !== 'object') {
        throw new TypeError('Metadata should be an object')
    }

    await Promise.all(promises)
}

async function testErrorEventEmission(): Promise<void> {
    const limiter = new Limiter({ maxRequestsPerSecond: 5 })

    let errorEventFired = false
    let caughtError: any = null

    limiter.on('error', (error) => {
        errorEventFired = true
        caughtError = error
    })

    // Create a function that throws an error
    const errorFunction = async () => {
        throw new Error('Test error')
    }

    try {
        await limiter.schedule(errorFunction)
        throw new Error('Should have thrown an error')
    } catch (error) {
        if ((error as Error).message !== 'Test error') {
            throw new Error('Should propagate the original error')
        }
    }
}

async function testConcurrentRequestsWithPriority(): Promise<void> {
    const limiter = new Limiter({ maxRequestsPerSecond: 1 })

    const results: string[] = []
    const startTime = Date.now()

    // Send requests with different priorities
    const promises = [
        limiter.schedule(async () => {
            results.push('low-priority')

            return 'low'
        }, { priority: 1 }),
        limiter.schedule(async () => {
            results.push('high-priority')

            return 'high'
        }, { priority: 9 }),
        limiter.schedule(async () => {
            results.push('medium-priority')

            return 'medium'
        }, { priority: 5 }),
    ]

    await Promise.all(promises)
    const duration = Date.now() - startTime

    if (results.length !== 3) {
        throw new Error('Should complete all 3 requests')
    }

    // Should take at least 2 seconds due to rate limiting (1 req/sec, 3 requests)
    if (duration < 1800) {
        throw new Error(`Should be rate limited, took only ${duration}ms`)
    }
}

async function testRequestWeight(): Promise<void> {
    const limiter = new Limiter({ maxRequestsPerSecond: 3 })

    const startTime = Date.now()

    // Send requests with different weights
    const promises = [
        limiter.schedule(createMockRequest('1'), { weight: 2 }),
        limiter.schedule(createMockRequest('2'), { weight: 1 }),
        limiter.schedule(createMockRequest('3'), { weight: 1 }),
    ]

    await Promise.all(promises)
    const duration = Date.now() - startTime

    // Total weight = 4, capacity = 3, so should be rate limited
    if (duration < 900) {
        throw new Error(`Should be rate limited due to weight, took only ${duration}ms`)
    }
}

async function testWindowReset(): Promise<void> {
    const limiter = new Limiter({ maxRequestsPerSecond: 2 })

    // Fill the first window
    await Promise.all([
        limiter.schedule(createMockRequest('1')),
        limiter.schedule(createMockRequest('2')),
    ])

    // Wait for window to reset (1 second + buffer)
    await delay(1100)

    const startTime = Date.now()

    // Should be able to send 2 more requests immediately
    await Promise.all([
        limiter.schedule(createMockRequest('3')),
        limiter.schedule(createMockRequest('4')),
    ])

    const duration = Date.now() - startTime

    if (duration > 500) {
        throw new Error(`Window should have reset, took ${duration}ms`)
    }
}

async function testRaceConditions(): Promise<void> {
    const limiter = new Limiter({ maxRequestsPerSecond: 3 })

    // Simulate race condition with many concurrent requests
    const requestCount = 10
    const promises: Array<Promise<string>> = []

    for (let i = 0; i < requestCount; i++) {
        promises.push(limiter.schedule(createMockRequest(i.toString(), 50)))
    }

    const startTime = Date.now()
    const results = await Promise.all(promises)
    const duration = Date.now() - startTime

    if (results.length !== requestCount) {
        throw new Error(`Should complete all ${requestCount} requests`)
    }

    // With 3 req/sec and 10 requests, should take at least 3 seconds
    if (duration < 2500) {
        throw new Error(`Race condition test should be rate limited, took only ${duration}ms`)
    }

    if (!results.every((r) => r.includes('completed'))) {
        throw new Error('All requests should complete successfully in race condition')
    }
}

async function testMultipleLimitEvents(): Promise<void> {
    const limiter = new Limiter({ maxRequestsPerSecond: 1 })

    const limitEvents: Array<{ until: Date, metadata: any }> = []

    limiter.on('limit', (until, metadata) => {
        limitEvents.push({ until, metadata })
    })

    // Send multiple batches to trigger multiple limit events
    const batch1 = [
        limiter.schedule(createMockRequest('1')),
        limiter.schedule(createMockRequest('2')),
    ]

    await delay(200)

    const batch2 = [
        limiter.schedule(createMockRequest('3')),
        limiter.schedule(createMockRequest('4')),
    ]

    await Promise.all([...batch1, ...batch2])

    if (limitEvents.length === 0) {
        throw new Error('Should emit at least one limit event')
    }

    // Check that all limit events have proper structure
    for (const event of limitEvents) {
        if (!(event.until instanceof Date)) {
            throw new TypeError('Each limit event should have Date until')
        }

        if (typeof event.metadata !== 'object') {
            throw new TypeError('Each limit event should have object metadata')
        }
    }
}

async function testZeroCapacityError(): Promise<void> {
    try {
        new Limiter({ maxRequestsPerSecond: 0 })
        throw new Error('Should throw error for zero capacity')
    } catch (error) {
        if (!(error as Error).message.includes('Invalid limiter configuration')) {
            throw new Error('Should throw specific configuration error')
        }
    }
}

async function testNegativeCapacityBehavior(): Promise<void> {
    // Note: Current implementation allows negative values and passes them to Bottleneck
    // This test verifies the current behavior rather than expecting an error
    const limiter = new Limiter({ maxRequestsPerSecond: -1 })

    // The limiter should be created but may behave unexpectedly
    if (!limiter) {
        throw new Error('Limiter should be created even with negative capacity')
    }

    // Try to schedule a request with timeout to avoid hanging
    try {
        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Request timed out')), 2000)
        })

        const requestPromise = limiter.schedule(createMockRequest('test'), { weight: 1 })

        await Promise.race([requestPromise, timeoutPromise])

        // If it succeeds, that's the current behavior
    } catch (error) {
        // If it fails or times out, that's acceptable behavior for negative capacity
        console.log('Negative capacity caused expected behavior:', (error as Error).message)
    }
}

async function testHighVolumeStability(): Promise<void> {
    const limiter = new Limiter({ maxRequestsPerSecond: 5 })

    // Test with high volume of requests
    const requestCount = 50
    const promises: Array<Promise<string>> = []

    for (let i = 0; i < requestCount; i++) {
        promises.push(limiter.schedule(createMockRequest(i.toString(), 10)))
    }

    const startTime = Date.now()
    const results = await Promise.all(promises)
    const duration = Date.now() - startTime

    if (results.length !== requestCount) {
        throw new Error(`Should complete all ${requestCount} requests`)
    }

    // With 5 req/sec and 50 requests, should take at least 9 seconds
    if (duration < 8000) {
        throw new Error(`High volume test should be properly rate limited, took only ${duration}ms`)
    }

    if (!results.every((r) => r.includes('completed'))) {
        throw new Error('All high volume requests should complete successfully')
    }
}

// Main test runner
async function runAllTests(): Promise<void> {
    console.log('🚀 Starting Limiter Tests')

    try {
        await runTest('Basic Limiter Creation', testBasicLimiterCreation)
        await runTest('Requests Under Limit', testRequestsUnderLimit)
        await runTest('Requests At Limit', testRequestsAtLimit)
        await runTest('Requests Over Limit', testRequestsOverLimit)
        await runTest('Limit Event Emission', testLimitEventEmission)
        await runTest('Error Event Emission', testErrorEventEmission)
        await runTest('Concurrent Requests with Priority', testConcurrentRequestsWithPriority)
        await runTest('Request Weight', testRequestWeight)
        await runTest('Window Reset', testWindowReset)
        await runTest('Race Conditions', testRaceConditions)
        await runTest('Multiple Limit Events', testMultipleLimitEvents)
        await runTest('Zero Capacity Error', testZeroCapacityError)
        await runTest('Negative Capacity Behavior', testNegativeCapacityBehavior)
        await runTest('High Volume Stability', testHighVolumeStability)

        console.log('\n🎉 All tests passed!')
    } catch {
        console.log('\n💥 Test suite failed!')
        process.exit(1)
    }
}

// Run tests
runAllTests().catch(console.error)
