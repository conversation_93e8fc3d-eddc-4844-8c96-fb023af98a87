import Bottleneck from 'bottleneck'

async function analyzeBottleneckTiming(): Promise<void> {
    console.log('🔍 Analyzing Bottleneck Internal Timing')
    
    const config = {
        reservoir: 2,
        reservoirRefreshInterval: 1000,
        reservoirRefreshAmount: 2,
        minTime: 0
    }
    
    const bottleneck = new Bottleneck(config)
    
    const events: Array<{
        type: string
        timestamp: number
        data?: any
    }> = []
    
    // Listen to all possible events
    bottleneck.on('depleted', (empty) => {
        events.push({
            type: 'depleted',
            timestamp: Date.now(),
            data: { empty }
        })
        console.log(`⚠️  DEPLETED at ${new Date().toISOString()}, empty: ${empty}`)
    })
    
    bottleneck.on('empty', () => {
        events.push({
            type: 'empty',
            timestamp: Date.now()
        })
        console.log(`📭 EMPTY at ${new Date().toISOString()}`)
    })
    
    bottleneck.on('idle', () => {
        events.push({
            type: 'idle',
            timestamp: Date.now()
        })
        console.log(`😴 IDLE at ${new Date().toISOString()}`)
    })
    
    bottleneck.on('debug', (message, data) => {
        if (message.includes('Drained') || message.includes('reservoir')) {
            console.log(`🐛 DEBUG: ${message}`, data)
        }
    })
    
    const mockJob = (id: string) => async () => {
        const start = Date.now()
        console.log(`📤 Job ${id} STARTED at ${new Date(start).toISOString()}`)
        await new Promise(resolve => setTimeout(resolve, 50))
        const end = Date.now()
        console.log(`✅ Job ${id} COMPLETED at ${new Date(end).toISOString()}`)
        return `result-${id}`
    }
    
    console.log(`\n🚀 Test started at: ${new Date().toISOString()}`)
    console.log(`⚙️  Config: reservoir=${config.reservoir}, refreshInterval=${config.reservoirRefreshInterval}ms`)
    
    // Check initial reservoir
    const initialReservoir = await bottleneck.currentReservoir()
    console.log(`🪣 Initial reservoir: ${initialReservoir}`)
    
    console.log('\n--- Phase 1: Fill reservoir (2 jobs) ---')
    await Promise.all([
        bottleneck.schedule(mockJob('1')),
        bottleneck.schedule(mockJob('2'))
    ])
    
    const reservoirAfterPhase1 = await bottleneck.currentReservoir()
    console.log(`🪣 Reservoir after phase 1: ${reservoirAfterPhase1}`)
    
    console.log('\n--- Phase 2: Trigger depleted (1 more job) ---')
    const phase2Start = Date.now()
    
    const job3Promise = bottleneck.schedule(mockJob('3'))
    
    // Wait a bit to see when job 3 actually starts
    await new Promise(resolve => setTimeout(resolve, 200))
    
    const reservoirDuringPhase2 = await bottleneck.currentReservoir()
    console.log(`🪣 Reservoir during phase 2: ${reservoirDuringPhase2}`)
    
    await job3Promise
    
    const phase2End = Date.now()
    console.log(`⏱️  Phase 2 duration: ${phase2End - phase2Start}ms`)
    
    console.log('\n--- Phase 3: Test refresh timing ---')
    const phase3Start = Date.now()
    
    await Promise.all([
        bottleneck.schedule(mockJob('4')),
        bottleneck.schedule(mockJob('5'))
    ])
    
    const phase3End = Date.now()
    console.log(`⏱️  Phase 3 duration: ${phase3End - phase3Start}ms`)
    
    console.log('\n--- Event Timeline ---')
    const startTime = events.length > 0 ? events[0].timestamp : Date.now()
    
    events.forEach((event, index) => {
        const relativeTime = event.timestamp - startTime
        console.log(`${index + 1}. ${event.type.toUpperCase()} at +${relativeTime}ms (${new Date(event.timestamp).toISOString()})`)
        if (event.data) {
            console.log(`   Data:`, event.data)
        }
    })
    
    await bottleneck.stop()
}

// Run analysis
analyzeBottleneckTiming().catch(console.error)
