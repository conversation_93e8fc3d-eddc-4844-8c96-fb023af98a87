{"type": "module", "private": true, "packageManager": "pnpm@10.11.1", "author": "<PERSON><PERSON> <<EMAIL>>", "license": "UNLICENSED", "engines": {"node": ">=20.12.0"}, "scripts": {"dev": "node bin/run.js", "start": "NODE_ENV=production node bin/run.js", "up": "ncu -i", "lint": "eslint .", "lint:fix": "eslint . --fix", "typecheck": "tsc --noEmit", "test:limiter": "node --import @swc-node/register/esm-register test/limiter.test.ts", "test:limiter:quick": "node --import @swc-node/register/esm-register test/limiter-quick.test.ts", "test:limiter:debug": "node --import @swc-node/register/esm-register test/limiter-until-debug.test.ts", "test:keep-alive": "node --import @swc-node/register/esm-register test/test-undici-keep-alive.ts", "test:keep-alive:quick": "node --import @swc-node/register/esm-register test/run-keep-alive-test.js --requests=5 --delay=200", "test:keep-alive:extended": "node --import @swc-node/register/esm-register test/run-keep-alive-test.js --requests=20 --delay=1000", "preinstall": "npx only-allow pnpm", "prepare": "simple-git-hooks"}, "dependencies": {"@kdt310722/config": "^0.0.4", "@kdt310722/logger": "^0.0.12", "@kdt310722/rpc": "^0.2.1", "@kdt310722/utils": "^0.0.19", "@swc-node/register": "^1.10.10", "better-sqlite3": "^11.10.0", "bottleneck": "^2.19.5", "p-queue": "^8.1.0", "pg": "^8.16.0", "pluralize": "^8.0.0", "reflect-metadata": "^0.2.2", "serialize-error": "^12.0.0", "synckit": "^0.11.8", "typeorm": "^0.3.24", "typeorm-naming-strategies": "^4.1.0", "uWebSockets.js": "github:uNetworking/uWebSockets.js#v20.52.0", "undici": "^7.10.0", "zod": "^3.25.51", "zod-validation-error": "^3.4.1"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@kdt-farm/eslint-config": "^0.0.2", "@kdt310722/tsconfig": "^1.0.0", "@types/node": "^22.15.29", "@types/pluralize": "^0.0.33", "eslint": "^9.28.0", "lint-staged": "^16.1.0", "npm-check-updates": "^18.0.1", "only-allow": "^1.2.1", "simple-git-hooks": "^2.13.0", "typescript": "^5.8.3"}, "commitlint": {"extends": "@commitlint/config-conventional"}, "simple-git-hooks": {"commit-msg": "npx --no -- commitlint --edit ${1}", "pre-commit": "npx tsc --noEmit && npx lint-staged"}, "lint-staged": {"*": "eslint --fix"}}